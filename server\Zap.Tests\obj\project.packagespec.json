﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\projects\\Zap\\server\\Zap.Tests\\Zap.Tests.csproj","projectName":"Zap.Tests","projectPath":"C:\\Users\\<USER>\\projects\\Zap\\server\\Zap.Tests\\Zap.Tests.csproj","outputPath":"C:\\Users\\<USER>\\projects\\Zap\\server\\Zap.Tests\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net9.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net9.0":{"targetAlias":"net9.0","projectReferences":{"C:\\Users\\<USER>\\projects\\Zap\\server\\Zap.Api\\Zap.Api.csproj":{"projectPath":"C:\\Users\\<USER>\\projects\\Zap\\server\\Zap.Api\\Zap.Api.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"},"SdkAnalysisLevel":"9.0.100"}"frameworks":{"net9.0":{"targetAlias":"net9.0","dependencies":{"Microsoft.AspNetCore.Hosting":{"target":"Package","version":"[2.3.0, )"},"Microsoft.AspNetCore.Mvc.Testing":{"target":"Package","version":"[9.0.3, )"},"Microsoft.EntityFrameworkCore":{"target":"Package","version":"[9.0.3, )"},"Microsoft.EntityFrameworkCore.InMemory":{"target":"Package","version":"[9.0.3, )"},"Microsoft.Extensions.Configuration":{"target":"Package","version":"[9.0.3, )"},"Microsoft.Extensions.DependencyInjection":{"target":"Package","version":"[9.0.3, )"},"Microsoft.NET.Test.Sdk":{"target":"Package","version":"[17.13.0, )"},"coverlet.collector":{"target":"Package","version":"[6.0.2, )"},"dotenv.net":{"target":"Package","version":"[3.2.1, )"},"xunit":{"target":"Package","version":"[2.9.2, )"},"xunit.runner.visualstudio":{"target":"Package","version":"[2.8.2, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}