{"format": 1, "restore": {"C:\\Users\\<USER>\\projects\\Zap\\server\\Zap.DataAccess\\Zap.DataAccess.csproj": {}}, "projects": {"C:\\Users\\<USER>\\projects\\Zap\\server\\Zap.DataAccess\\Zap.DataAccess.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\projects\\Zap\\server\\Zap.DataAccess\\Zap.DataAccess.csproj", "projectName": "Zap.DataAccess", "projectPath": "C:\\Users\\<USER>\\projects\\Zap\\server\\Zap.DataAccess\\Zap.DataAccess.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\projects\\Zap\\server\\Zap.DataAccess\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[3.7.415.24, )"}, "Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.3, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.Extensions.Configuration.FileExtensions": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.3, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}}}