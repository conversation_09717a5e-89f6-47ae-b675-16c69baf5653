<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.400" />
        <PackageReference Include="AWSSDK.S3" Version="3.7.416.5" />
        <PackageReference Include="Bogus" Version="35.6.3" />
        <PackageReference Include="dotenv.net" Version="3.2.1" />
        <PackageReference Include="FluentValidation" Version="11.11.0" />
        <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.3" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.1" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.3">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
        <PackageReference Include="Scalar.AspNetCore" Version="2.1.0" />
        <PackageReference Include="Serilog" Version="4.2.0" />
        <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.1" />
        <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Features\" />
    </ItemGroup>
  
    <ItemGroup Condition="'$(Configuration)' == 'Debug'">
        <None Update=".env" CopyToOutputDirectory="PreserveNewest" />
    </ItemGroup>
</Project>
