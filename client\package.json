{"name": "client", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc"}, "dependencies": {"@remix-run/node": "^2.16.1", "@remix-run/react": "^2.16.1", "@remix-run/serve": "^2.16.1", "@tailwindcss/postcss": "^4.0.14", "isbot": "^4.1.0", "openapi-fetch": "^0.13.5", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^4.0.14"}, "devDependencies": {"@remix-run/dev": "^2.16.1", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.19", "daisyui": "^5.0.6", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "openapi-typescript": "^7.6.1", "postcss": "^8.5.3", "typescript": "^5.1.6", "vite": "^6.0.0", "vite-tsconfig-paths": "^4.2.1"}, "engines": {"node": ">=20.0.0"}, "pnpm": {"ignoredBuiltDependencies": ["esbuild"], "onlyBuiltDependencies": ["esbuild"]}}